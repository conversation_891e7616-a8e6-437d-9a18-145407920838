<?php
/**
 * Minimal Admin Test Page
 * Tests basic admin functionality without complex includes
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Simple database connection test
try {
    include '../db.php';
    $db_status = "✅ Database connected";
} catch (Exception $e) {
    $db_status = "❌ Database error: " . $e->getMessage();
}

// Simple config test
try {
    include '../config.php';
    $config_status = "✅ Config loaded";
} catch (Exception $e) {
    $config_status = "❌ Config error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Minimal Admin Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h1>🧪 Minimal Admin Test</h1>
                    <p>This page tests basic admin functionality without complex includes.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h3>System Status</h3>
                    <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                    <p><strong>Database:</strong> <span class="<?php echo strpos($db_status, '✅') !== false ? 'success' : 'error'; ?>"><?php echo $db_status; ?></span></p>
                    <p><strong>Config:</strong> <span class="<?php echo strpos($config_status, '✅') !== false ? 'success' : 'error'; ?>"><?php echo $config_status; ?></span></p>
                    <p><strong>Environment:</strong> <?php echo defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Not defined'; ?></p>
                    <p><strong>Base URL:</strong> <?php echo defined('BASE_URL') ? BASE_URL : 'Not defined'; ?></p>
                </div>
            </div>

            <div class="col-md-6">
                <div class="test-card">
                    <h3>Session Status</h3>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <p class="success">✅ User is logged in</p>
                        <p><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                        <p><strong>Username:</strong> <?php echo $_SESSION['username'] ?? 'Not set'; ?></p>
                        <p><strong>Role:</strong> <?php echo $_SESSION['role'] ?? 'Not set'; ?></p>
                        
                        <?php if ($_SESSION['role'] === 'Admin'): ?>
                            <p class="success">✅ Admin access granted</p>
                        <?php else: ?>
                            <p class="error">❌ Not an admin user</p>
                        <?php endif; ?>
                    <?php else: ?>
                        <p class="warning">⚠️ User is not logged in</p>
                        <p><a href="../login.php" class="btn btn-primary">Login</a></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'Admin'): ?>
        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h3>Quick Stats</h3>
                    <?php
                    try {
                        $users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
                        echo "<p><strong>Total Users:</strong> $users_count</p>";
                    } catch (Exception $e) {
                        echo "<p class='error'>Error loading user count: " . $e->getMessage() . "</p>";
                    }

                    try {
                        $tests_count = $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'];
                        echo "<p><strong>Total Tests:</strong> $tests_count</p>";
                    } catch (Exception $e) {
                        echo "<p class='error'>Error loading test count: " . $e->getMessage() . "</p>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h3>Admin Actions</h3>
                    <p>If this page loads successfully, the basic admin functionality is working.</p>
                    <div class="btn-group" role="group">
                        <a href="index.php" class="btn btn-primary">Try Full Admin Panel</a>
                        <a href="users.php" class="btn btn-secondary">Manage Users</a>
                        <a href="tests.php" class="btn btn-secondary">Manage Tests</a>
                        <a href="../index.php" class="btn btn-outline-primary">Back to Site</a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="test-card">
                    <h3>Debug Information</h3>
                    <p><strong>Current File:</strong> <?php echo __FILE__; ?></p>
                    <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Not set'; ?></p>
                    <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Not set'; ?></p>
                    <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Not set'; ?></p>
                    <p><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Not set'; ?></p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
