<?php
/**
 * Emergency Admin User Creator
 * Creates an admin user for accessing the admin panel
 * DELETE THIS FILE AFTER USE FOR SECURITY
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
include 'db.php';
include 'config.php';

echo "<h1>🔐 Admin User Creator</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
    .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #f0f0f8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .form-group { margin: 15px 0; }
    .form-control { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    .btn:hover { background: #0056b3; }
    .btn-danger { background: #dc3545; }
    .btn-danger:hover { background: #c82333; }
</style>";

echo "<div class='container'>";

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    $errors = [];
    
    if (empty($username)) {
        $errors[] = "Username is required";
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Valid email is required";
    }
    
    if (empty($password) || strlen($password) < 6) {
        $errors[] = "Password must be at least 6 characters";
    }
    
    if ($password !== $confirm_password) {
        $errors[] = "Passwords do not match";
    }
    
    // Check if user already exists
    if (empty($errors)) {
        $check_stmt = $con->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $check_stmt->bind_param("ss", $email, $username);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errors[] = "User with this email or username already exists";
        }
    }
    
    // Create admin user if no errors
    if (empty($errors)) {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $role = 'Admin';
        $credits = 100; // Default credits
        
        $stmt = $con->prepare("INSERT INTO users (username, email, password, role, credits, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssssi", $username, $email, $hashed_password, $role, $credits);
        
        if ($stmt->execute()) {
            echo "<div class='success'>";
            echo "<h3>✅ Admin User Created Successfully!</h3>";
            echo "<p><strong>Username:</strong> $username</p>";
            echo "<p><strong>Email:</strong> $email</p>";
            echo "<p><strong>Role:</strong> Admin</p>";
            echo "<p>You can now login with these credentials.</p>";
            echo "<p><a href='login.php' class='btn'>Go to Login</a></p>";
            echo "</div>";
            
            echo "<div class='warning'>";
            echo "<h4>🚨 Security Warning</h4>";
            echo "<p>Delete this file immediately after creating the admin user!</p>";
            echo "<p>This file poses a security risk if left on the server.</p>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h3>❌ Error Creating User</h3>";
            echo "<p>Database error: " . htmlspecialchars($con->error) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ Validation Errors</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
}

// Check existing admin users
echo "<div class='info'>";
echo "<h3>📊 Current Admin Users</h3>";
try {
    $admin_result = $con->query("SELECT username, email, created_at FROM users WHERE role = 'Admin' ORDER BY created_at DESC");
    if ($admin_result->num_rows > 0) {
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Username</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Email</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd;'>Created</th>";
        echo "</tr>";
        
        while ($admin = $admin_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($admin['username']) . "</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($admin['email']) . "</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($admin['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p class='success'>✅ Admin users exist. You can login with any of the above credentials.</p>";
    } else {
        echo "<p class='warning'>⚠️ No admin users found. Create one below.</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking admin users: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Show form only if no admin users exist or if there was an error
$show_form = true;
try {
    $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
    if ($admin_count > 0 && $_SERVER['REQUEST_METHOD'] !== 'POST') {
        $show_form = false;
    }
} catch (Exception $e) {
    // Show form if we can't check
}

if ($show_form || isset($_POST['force_show'])) {
?>

<h3>🆕 Create New Admin User</h3>
<form method="POST">
    <div class="form-group">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" class="form-control" required 
               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
    </div>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" name="email" class="form-control" required
               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" class="form-control" required minlength="6">
        <small>Minimum 6 characters</small>
    </div>
    
    <div class="form-group">
        <label for="confirm_password">Confirm Password:</label>
        <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
    </div>
    
    <button type="submit" class="btn">Create Admin User</button>
</form>

<?php
} else {
    echo "<form method='POST'>";
    echo "<input type='hidden' name='force_show' value='1'>";
    echo "<button type='submit' class='btn'>Create Another Admin User</button>";
    echo "</form>";
}
?>

<h3>🔗 Quick Links</h3>
<p>
    <a href="login.php" class="btn">Login Page</a>
    <a href="admin/index.php" class="btn">Admin Panel</a>
    <a href="index.php" class="btn">Main Site</a>
</p>

<div class="error">
    <h4>🚨 IMPORTANT SECURITY NOTICE</h4>
    <p><strong>DELETE THIS FILE AFTER USE!</strong></p>
    <p>This file allows anyone to create admin users and poses a serious security risk.</p>
    <p>Remove it from your server immediately after creating your admin account.</p>
</div>

</div>
