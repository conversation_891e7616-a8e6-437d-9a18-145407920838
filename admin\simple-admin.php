<?php
// Start session first
session_start();

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include config
include '../config.php';

// Check authentication
if (!isset($_SESSION['user_id'])) {
    header('Location: ../admin-login.php');
    exit();
}

if ($_SESSION['role'] !== 'Admin') {
    die('Access denied. Admin privileges required.');
}

// Get stats
try {
    $users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $tests_count = $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'];
    $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
} catch (Exception $e) {
    $users_count = $tests_count = $admin_count = 'Error';
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>پانێڵی بەڕێوەبەر - Simple Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
        }
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .admin-nav a {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .admin-nav a:hover {
            background: #764ba2;
            transform: translateY(-2px);
            color: white;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Simple Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="simple-admin.php">
                <i class="fas fa-tachometer-alt"></i>
                پانێڵی بەڕێوەبەر
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../index.php">
                    <i class="fas fa-home"></i> ماڵپەڕ
                </a>
                <a class="nav-link" href="../admin-login.php?logout=1">
                    <i class="fas fa-sign-out-alt"></i> چوونەدەرەوە
                </a>
            </div>
        </div>
    </nav>

    <div class="admin-container">
        <div class="container">
            <!-- Welcome Card -->
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <div class="alert alert-success">
                            <h1 class="text-center mb-4">
                                <i class="fas fa-check-circle"></i>
                                پانێڵی بەڕێوەبەر کارا دەکات!
                            </h1>
                            <p class="text-center text-muted">بەخێربێیت <?php echo $_SESSION['username']; ?></p>
                            <p class="text-center">
                                <small>
                                    <strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?> | 
                                    <strong>Role:</strong> <?php echo $_SESSION['role']; ?> | 
                                    <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                                </small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $users_count; ?></span>
                        <span class="stat-label">بەکارهێنەر</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $tests_count; ?></span>
                        <span class="stat-label">تێست</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $admin_count; ?></span>
                        <span class="stat-label">بەڕێوەبەر</span>
                    </div>
                </div>
            </div>

            <!-- Admin Functions -->
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h3 class="mb-3 text-center">بەڕێوەبردنی سیستەم</h3>
                        <div class="admin-nav text-center">
                            <a href="users.php">
                                <i class="fas fa-users"></i>
                                بەڕێوەبردنی بەکارهێنەران
                            </a>
                            <a href="tests.php">
                                <i class="fas fa-clipboard-list"></i>
                                بەڕێوەبردنی تێستەکان
                            </a>
                            <a href="transactions.php">
                                <i class="fas fa-exchange-alt"></i>
                                بەڕێوەبردنی مامەڵەکان
                            </a>
                            <a href="index.php">
                                <i class="fas fa-tachometer-alt"></i>
                                پانێڵی ئەسڵی
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="row">
                <div class="col-md-6">
                    <div class="admin-card">
                        <h5>دۆخی سیستەم</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Database:</strong></td>
                                <td><span class="text-success">✅ Connected</span></td>
                            </tr>
                            <tr>
                                <td><strong>Environment:</strong></td>
                                <td><?php echo defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Base URL:</strong></td>
                                <td><?php echo defined('BASE_URL') ? BASE_URL : 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>PHP Version:</strong></td>
                                <td><?php echo phpversion(); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="admin-card">
                        <h5>دەستکەوتەکان</h5>
                        <div class="alert alert-success">
                            <h6>✅ کێشەکان چارەسەر کران!</h6>
                            <ul class="mb-0">
                                <li>Session کارا دەکات</li>
                                <li>Database پەیوەست بووە</li>
                                <li>Admin access کارا دەکات</li>
                                <li>هەموو فایلەکان بەردەستن</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h5>بەکارهێنەرانی نوێ</h5>
                        <?php
                        try {
                            $recent_users = $con->query("SELECT username, email, role, created_at FROM users ORDER BY created_at DESC LIMIT 5");
                            if ($recent_users->num_rows > 0) {
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-striped'>";
                                echo "<thead><tr><th>ناو</th><th>ئیمەیڵ</th><th>ڕۆڵ</th><th>بەروار</th></tr></thead>";
                                echo "<tbody>";
                                while ($user = $recent_users->fetch_assoc()) {
                                    echo "<tr>";
                                    echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                                    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                                    echo "<td><span class='badge bg-" . ($user['role'] === 'Admin' ? 'danger' : 'primary') . "'>" . $user['role'] . "</span></td>";
                                    echo "<td>" . $user['created_at'] . "</td>";
                                    echo "</tr>";
                                }
                                echo "</tbody></table>";
                                echo "</div>";
                            } else {
                                echo "<p class='text-muted'>هیچ بەکارهێنەرێک نەدۆزرایەوە</p>";
                            }
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>خەتا لە بارکردنی بەکارهێنەران: " . htmlspecialchars($e->getMessage()) . "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
