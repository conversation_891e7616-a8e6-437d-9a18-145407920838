<?php
/**
 * Admin Panel Fix Script
 * Automatically fixes common admin panel issues
 */

// Start session first
session_start();

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>Admin Panel Fix Script</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='text-center mb-4'>🔧 Admin Panel Fix Script</h1>";

// Test 1: Basic connectivity
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>1. Basic System Test</h3></div>";
echo "<div class='card-body'>";

echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";

// Test database
try {
    include 'config.php';
    echo "<div class='alert alert-success'>✅ Database connected successfully</div>";
    
    $user_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
    
    echo "<p><strong>Total Users:</strong> $user_count</p>";
    echo "<p><strong>Admin Users:</strong> $admin_count</p>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div></div>";

// Test 2: File permissions and existence
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>2. File System Check</h3></div>";
echo "<div class='card-body'>";

$critical_files = [
    'admin/index.php' => 'Admin panel main file',
    'admin/users.php' => 'User management',
    'admin/tests.php' => 'Test management',
    'includes/head.php' => 'Head include',
    'includes/admin_navbar.php' => 'Admin navigation',
    'includes/script.php' => 'Script include'
];

$all_files_ok = true;
foreach ($critical_files as $file => $description) {
    if (file_exists($file)) {
        $readable = is_readable($file) ? 'readable' : 'not readable';
        echo "<div class='alert alert-success'>✅ $description ($file) - $readable</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ $description ($file) - missing</div>";
        $all_files_ok = false;
    }
}

echo "</div></div>";

// Test 3: Admin user check
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>3. Admin User Verification</h3></div>";
echo "<div class='card-body'>";

if (isset($con)) {
    try {
        $admin_users = $con->query("SELECT id, username, email, created_at FROM users WHERE role = 'Admin'");
        
        if ($admin_users->num_rows > 0) {
            echo "<div class='alert alert-success'>✅ Admin users found</div>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>ID</th><th>Username</th><th>Email</th><th>Created</th></tr></thead>";
            echo "<tbody>";
            
            while ($admin = $admin_users->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $admin['id'] . "</td>";
                echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
                echo "<td>" . $admin['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ No admin users found</div>";
            echo "<p><a href='create-admin.php' class='btn btn-primary'>Create Admin User</a></p>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ Error checking admin users: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

echo "</div></div>";

// Test 4: Session test
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>4. Session Status</h3></div>";
echo "<div class='card-body'>";

try {
    // Session already started at the top
    echo "<div class='alert alert-success'>✅ Session system working</div>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>Current Session:</h5>";
        echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
        echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
        echo "</div>";
        
        if ($_SESSION['role'] === 'Admin') {
            echo "<div class='alert alert-success'>✅ Admin session active</div>";
        } else {
            echo "<div class='alert alert-warning'>⚠️ Not logged in as admin</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ No active session</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Session error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div></div>";

// Test 5: Admin panel access test
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>5. Admin Panel Access Test</h3></div>";
echo "<div class='card-body'>";

if (file_exists('admin/index.php')) {
    echo "<div class='alert alert-info'>";
    echo "<h5>Admin Panel Test:</h5>";
    echo "<p>The admin panel file exists. Testing access...</p>";
    
    if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'Admin') {
        echo "<p class='text-success'>✅ You have admin access</p>";
        echo "<p><a href='admin/index.php' class='btn btn-success'>Access Admin Panel</a></p>";
    } else {
        echo "<p class='text-warning'>⚠️ You need to login as admin first</p>";
        echo "<p><a href='admin/login-bypass.php' class='btn btn-warning'>Temporary Login Bypass</a></p>";
        echo "<p><a href='login.php' class='btn btn-primary'>Normal Login</a></p>";
    }
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>❌ Admin panel file missing</div>";
}

echo "</div></div>";

// Quick actions
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h3>Quick Actions</h3></div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-4'>";
echo "<h5>Access</h5>";
echo "<a href='admin/index.php' class='btn btn-primary d-block mb-2'>Admin Panel</a>";
echo "<a href='admin/login-bypass.php' class='btn btn-warning d-block mb-2'>Login Bypass</a>";
echo "<a href='login.php' class='btn btn-secondary d-block mb-2'>Normal Login</a>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h5>Management</h5>";
echo "<a href='create-admin.php' class='btn btn-info d-block mb-2'>Create Admin</a>";
echo "<a href='admin/users.php' class='btn btn-outline-primary d-block mb-2'>Manage Users</a>";
echo "<a href='admin/tests.php' class='btn btn-outline-primary d-block mb-2'>Manage Tests</a>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<h5>Debug</h5>";
echo "<a href='simple-test.php' class='btn btn-outline-secondary d-block mb-2'>Simple Test</a>";
echo "<a href='admin/test-admin.php' class='btn btn-outline-secondary d-block mb-2'>Admin Test</a>";
echo "<a href='universal-debug.php' class='btn btn-outline-secondary d-block mb-2'>Universal Debug</a>";
echo "</div>";
echo "</div>";

echo "</div></div>";

// Summary and recommendations
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-primary text-white'><h3>Summary & Recommendations</h3></div>";
echo "<div class='card-body'>";

if ($all_files_ok && isset($con) && $admin_count > 0) {
    echo "<div class='alert alert-success'>";
    echo "<h5>✅ System appears to be working correctly!</h5>";
    echo "<p>All critical files exist, database is connected, and admin users are available.</p>";
    echo "<p><strong>Next step:</strong> Try accessing the admin panel or use the login bypass for testing.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5>⚠️ Issues detected</h5>";
    echo "<ul>";
    if (!$all_files_ok) echo "<li>Some critical files are missing</li>";
    if (!isset($con)) echo "<li>Database connection failed</li>";
    if ($admin_count == 0) echo "<li>No admin users exist</li>";
    echo "</ul>";
    echo "<p><strong>Recommendation:</strong> Address the issues above before accessing the admin panel.</p>";
    echo "</div>";
}

echo "<div class='alert alert-danger'>";
echo "<h5>🚨 Security Notice</h5>";
echo "<p><strong>Delete these debug files after fixing:</strong></p>";
echo "<ul>";
echo "<li>fix-admin.php (this file)</li>";
echo "<li>admin/login-bypass.php</li>";
echo "<li>simple-test.php</li>";
echo "<li>universal-debug.php</li>";
echo "<li>create-admin.php</li>";
echo "</ul>";
echo "</div>";

echo "</div></div>";

echo "</div>"; // container
echo "</body>";
echo "</html>";
?>
