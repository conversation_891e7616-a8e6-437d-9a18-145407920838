# Allow PHP execution in admin directory
<Files "*.php">
    Order allow,deny
    Allow from all
    Require all granted
</Files>

# Enable PHP processing
AddHandler application/x-httpd-php .php

# Allow directory access
Options +Indexes +FollowSymLinks

# Remove any restrictions
<RequireAll>
    Require all granted
</RequireAll>

# Error handling
ErrorDocument 403 "Access restored - please try again"
ErrorDocument 500 "Server error - check file permissions"
