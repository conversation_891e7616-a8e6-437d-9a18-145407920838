<?php
/**
 * Admin Panel Diagnostic Tool
 * Diagnoses admin panel 500 errors
 * DELETE AFTER FIXING
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Admin Panel Diagnostic</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #f0f0f8; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

echo "<div class='info'>";
echo "<h2>🔍 Admin Panel Error Diagnosis</h2>";
echo "<p>Testing admin panel components step by step.</p>";
echo "</div>";

// Test 1: Basic PHP
echo "<h3>1. Basic PHP Test</h3>";
echo "<p class='success'>✅ PHP is working (you can see this page)</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";

// Test 2: File existence
echo "<h3>2. Admin File Check</h3>";
$admin_files = ['index.php', 'users.php', 'tests.php', 'transactions.php', 'edit_user.php'];
foreach ($admin_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $file exists</p>";
    } else {
        echo "<p class='error'>❌ $file missing</p>";
    }
}

// Test 3: Parent directory files
echo "<h3>3. Parent Directory Files</h3>";
$parent_files = ['../db.php', '../config.php', '../head.php'];
foreach ($parent_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $file exists</p>";
    } else {
        echo "<p class='error'>❌ $file missing</p>";
    }
}

// Test 4: Include test
echo "<h3>4. Include Test</h3>";
try {
    echo "<p>Testing ../db.php...</p>";
    include '../db.php';
    echo "<p class='success'>✅ db.php included successfully</p>";
    
    if (isset($con) && $con instanceof mysqli) {
        echo "<p class='success'>✅ Database connection working</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error including db.php: " . htmlspecialchars($e->getMessage()) . "</p>";
}

try {
    echo "<p>Testing ../config.php...</p>";
    include '../config.php';
    echo "<p class='success'>✅ config.php included successfully</p>";
    
    if (function_exists('url')) {
        echo "<p class='success'>✅ URL functions available</p>";
    } else {
        echo "<p class='error'>❌ URL functions not available</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error including config.php: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Session test
echo "<h3>5. Session Test</h3>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p class='success'>✅ Session started successfully</p>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'>✅ User logged in: " . ($_SESSION['username'] ?? 'Unknown') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Unknown') . "</p>";
    } else {
        echo "<p class='warning'>⚠️ No user logged in</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Session error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 6: .htaccess check
echo "<h3>6. .htaccess Check</h3>";
if (file_exists('../.htaccess')) {
    echo "<p class='success'>✅ .htaccess exists in parent directory</p>";
    
    $htaccess = file_get_contents('../.htaccess');
    if (strpos($htaccess, 'admin') !== false) {
        echo "<p class='info'>ℹ️ .htaccess contains admin-related rules</p>";
    }
    
    // Check for problematic rules
    if (strpos($htaccess, '<Files "*-check.php">') !== false) {
        echo "<p class='error'>❌ .htaccess has problematic rules blocking diagnostic files</p>";
    }
} else {
    echo "<p class='warning'>⚠️ No .htaccess in parent directory</p>";
}

if (file_exists('.htaccess')) {
    echo "<p class='info'>ℹ️ .htaccess exists in admin directory</p>";
} else {
    echo "<p class='success'>✅ No .htaccess in admin directory (good)</p>";
}

// Test 7: Memory and limits
echo "<h3>7. PHP Limits</h3>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</p>";
echo "<p><strong>Current Memory Usage:</strong> " . memory_get_usage(true) . " bytes</p>";

// Test 8: Create minimal admin index
echo "<h3>8. Quick Fix Options</h3>";

if (isset($_POST['create_minimal_admin'])) {
    $minimal_admin = '<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);

session_start();
include "../db.php";
include "../config.php";

// Simple admin check
if (!isset($_SESSION["user_id"]) || $_SESSION["role"] !== "Admin") {
    echo "<h1>Access Denied</h1>";
    echo "<p>Please <a href=\"../login.php\">login</a> as admin.</p>";
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel - Minimal Mode</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🛠️ Admin Panel - Minimal Mode</h1>
        <div class="alert alert-success">
            <h4>✅ Admin Panel Working</h4>
            <p>Welcome, <?php echo htmlspecialchars($_SESSION["username"] ?? "Admin"); ?>!</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5>Quick Stats</h5>
                        <?php
                        try {
                            $result = $con->query("SELECT COUNT(*) as count FROM users");
                            $users = $result->fetch_assoc()["count"];
                            echo "<p>Total Users: $users</p>";
                        } catch (Exception $e) {
                            echo "<p>Error loading stats</p>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="../index.php" class="btn btn-primary">Back to Website</a>
            <a href="admin-diagnostic.php" class="btn btn-secondary">Run Diagnostic Again</a>
        </div>
    </div>
</body>
</html>';

    // Backup current admin index
    if (file_exists('index.php')) {
        copy('index.php', 'index.php.backup.' . date('Y-m-d-H-i-s'));
    }
    
    file_put_contents('index.php', $minimal_admin);
    echo "<div class='success'>";
    echo "<h4>✅ Minimal Admin Created</h4>";
    echo "<p>Backup saved and minimal admin panel created.</p>";
    echo "<p><a href='index.php'>Test Admin Panel</a></p>";
    echo "</div>";
} else {
    echo "<div class='warning'>";
    echo "<h4>🚑 Emergency Fix</h4>";
    echo "<p>Create a minimal admin panel to test basic functionality:</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='create_minimal_admin' class='btn btn-warning'>Create Minimal Admin Panel</button>";
    echo "</form>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h3>📞 Next Steps</h3>";
echo "<ol>";
echo "<li>If minimal admin works, the issue is in the complex admin code</li>";
echo "<li>Check server error logs for specific errors</li>";
echo "<li>Gradually restore admin features</li>";
echo "<li>Consider simplifying the admin panel structure</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>DELETE THIS FILE AFTER FIXING THE ADMIN PANEL!</strong></p>";
echo "</div>";
?>
