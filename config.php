<?php
/**
 * Configuration File
 * Auto-detects environment and configures accordingly
 */

// Include database connection (which sets CURRENT_ENVIRONMENT and BASE_URL)
require_once 'db.php';

// Environment-specific configuration
$configs = [
    'localhost' => [
        'site_name' => 'ئیلهامبەخشی دەروونی',
        'admin_url' => BASE_URL . 'admin/',
        'api_url' => BASE_URL . 'api/',
        'assets_url' => BASE_URL . 'assets/',
        'uploads_url' => BASE_URL . 'uploads/',
        'debug_mode' => true,
        'error_reporting' => true
    ],
    'production' => [
        'site_name' => 'ئیلهامبەخشی دەروونی',
        'admin_url' => 'https://inspiremental.org/admin/',
        'api_url' => 'https://inspiremental.org/api/',
        'assets_url' => 'https://inspiremental.org/assets/',
        'uploads_url' => 'https://inspiremental.org/uploads/',
        'debug_mode' => false,
        'error_reporting' => false
    ]
];

$currentConfig = $configs[CURRENT_ENVIRONMENT];

// Define global constants
define('SITE_NAME', $currentConfig['site_name']);
define('ADMIN_URL', $currentConfig['admin_url']);
define('API_URL', $currentConfig['api_url']);
define('ASSETS_URL', $currentConfig['assets_url']);
define('UPLOADS_URL', $currentConfig['uploads_url']);
define('DEBUG_MODE', $currentConfig['debug_mode']);

// Set error reporting based on environment
if ($currentConfig['error_reporting']) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    // Temporarily enable error display for debugging
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
}

/**
 * Helper Functions
 */

// Generate URL for pages
function url($path = '') {
    return BASE_URL . ltrim($path, '/');
}

// Generate admin URL
function admin_url($path = '') {
    return ADMIN_URL . ltrim($path, '/');
}

// Generate API URL
function api_url($path = '') {
    return API_URL . ltrim($path, '/');
}

// Generate assets URL
function assets_url($path = '') {
    return ASSETS_URL . ltrim($path, '/');
}

// Generate uploads URL
function uploads_url($path = '') {
    return UPLOADS_URL . ltrim($path, '/');
}

// Environment detection functions
function is_development() {
    return CURRENT_ENVIRONMENT === 'localhost';
}

function is_production() {
    return CURRENT_ENVIRONMENT === 'production';
}

// Debug function - works based on environment
function debug($data, $label = '') {
    if (is_development()) {
        echo '<pre>';
        if ($label) echo '<strong>' . htmlspecialchars($label) . ':</strong><br>';
        print_r($data);
        echo '</pre>';
    }
}
?>