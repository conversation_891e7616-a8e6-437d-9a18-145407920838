<?php
/**
 * Emergency Admin Panel
 * Bypasses normal authentication for debugging
 * DELETE AFTER USE - SECURITY RISK!
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include database and config
try {
    include '../db.php';
    include '../config.php';
    $db_status = "✅ Connected";
} catch (Exception $e) {
    $db_status = "❌ Error: " . $e->getMessage();
    die("Database connection failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Emergency Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .emergency-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px auto;
            max-width: 1200px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .btn-emergency {
            background: #dc3545;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
        }
        .btn-emergency:hover {
            background: #c82333;
            color: white;
        }
    </style>
</head>
<body>
    <div class="emergency-container">
        <div class="text-center mb-4">
            <h1 class="text-danger">🚨 Emergency Admin Panel</h1>
            <p class="text-muted">Bypass mode - For debugging only</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="status-card success">
                    <h4>System Status</h4>
                    <p><strong>Database:</strong> <?php echo $db_status; ?></p>
                    <p><strong>Environment:</strong> <?php echo defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Unknown'; ?></p>
                    <p><strong>Base URL:</strong> <?php echo defined('BASE_URL') ? BASE_URL : 'Unknown'; ?></p>
                    <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                </div>
            </div>

            <div class="col-md-6">
                <div class="status-card warning">
                    <h4>Session Status</h4>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <p><strong>Logged in as:</strong> <?php echo $_SESSION['username'] ?? 'Unknown'; ?></p>
                        <p><strong>Role:</strong> <?php echo $_SESSION['role'] ?? 'Unknown'; ?></p>
                        <p><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                    <?php else: ?>
                        <p class="text-warning">⚠️ No active session</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row">
            <?php
            try {
                $users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
                $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
                $tests_count = $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'];
            } catch (Exception $e) {
                $users_count = $admin_count = $tests_count = 'Error';
            }
            ?>
            <div class="col-md-4">
                <div class="stat-card">
                    <span class="stat-number"><?php echo $users_count; ?></span>
                    <span>Total Users</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <span class="stat-number"><?php echo $admin_count; ?></span>
                    <span>Admin Users</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <span class="stat-number"><?php echo $tests_count; ?></span>
                    <span>Tests</span>
                </div>
            </div>
        </div>

        <div class="status-card">
            <h4>Quick Actions</h4>
            <div class="row">
                <div class="col-md-6">
                    <h5>Admin Functions</h5>
                    <a href="users.php" class="btn btn-primary">
                        <i class="fas fa-users"></i> Manage Users
                    </a>
                    <a href="tests.php" class="btn btn-primary">
                        <i class="fas fa-clipboard-list"></i> Manage Tests
                    </a>
                    <a href="transactions.php" class="btn btn-primary">
                        <i class="fas fa-exchange-alt"></i> Transactions
                    </a>
                </div>
                <div class="col-md-6">
                    <h5>Debug Tools</h5>
                    <a href="debug.php" class="btn btn-info">
                        <i class="fas fa-bug"></i> Debug Tool
                    </a>
                    <a href="test-minimal.php" class="btn btn-info">
                        <i class="fas fa-vial"></i> Minimal Test
                    </a>
                    <a href="../universal-debug.php" class="btn btn-info">
                        <i class="fas fa-tools"></i> Universal Debug
                    </a>
                </div>
            </div>
        </div>

        <div class="status-card">
            <h4>User Management</h4>
            <?php
            try {
                $admin_users = $con->query("SELECT id, username, email, created_at FROM users WHERE role = 'Admin' ORDER BY created_at DESC LIMIT 5");
                if ($admin_users->num_rows > 0) {
                    echo "<h5>Admin Users:</h5>";
                    echo "<div class='table-responsive'>";
                    echo "<table class='table table-sm'>";
                    echo "<thead><tr><th>ID</th><th>Username</th><th>Email</th><th>Created</th></tr></thead>";
                    echo "<tbody>";
                    while ($admin = $admin_users->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . $admin['id'] . "</td>";
                        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
                        echo "<td>" . $admin['created_at'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-warning'>No admin users found!</div>";
                    echo "<a href='../create-admin.php' class='btn btn-warning'>Create Admin User</a>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>Error loading users: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            ?>
        </div>

        <div class="status-card">
            <h4>Navigation</h4>
            <a href="../index.php" class="btn btn-secondary">
                <i class="fas fa-home"></i> Main Site
            </a>
            <a href="../login.php" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login Page
            </a>
            <a href="index.php" class="btn btn-success">
                <i class="fas fa-tachometer-alt"></i> Try Normal Admin Panel
            </a>
        </div>

        <div class="status-card error">
            <h4>🚨 Security Warning</h4>
            <p><strong>This is an emergency bypass tool!</strong></p>
            <p>This file bypasses normal security checks and should be deleted immediately after debugging.</p>
            <p>It poses a serious security risk if left on the server.</p>
            <button class="btn btn-emergency" onclick="if(confirm('Are you sure you want to delete this file?')) { window.location.href='?delete_emergency=1'; }">
                <i class="fas fa-trash"></i> Delete This File
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// Handle file deletion
if (isset($_GET['delete_emergency'])) {
    if (unlink(__FILE__)) {
        echo "<script>alert('Emergency file deleted successfully!'); window.location.href='index.php';</script>";
    } else {
        echo "<script>alert('Failed to delete file. Please delete manually.');</script>";
    }
}
?>
