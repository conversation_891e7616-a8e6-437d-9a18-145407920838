<?php
// Test management in root directory
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
} else {
    $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) die("Database failed: " . $con->connect_error);
    $con->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: simple-login.php');
    exit();
}

// Handle test deletion
$message = '';
if (isset($_POST['delete_test'])) {
    $test_id = (int)$_POST['test_id'];
    $con->query("DELETE FROM tests WHERE id = $test_id");
    $message = "Test deleted successfully";
}

// Fetch tests from the database with user information
$result = $con->query("
    SELECT t.id, t.user_id, t.test_type, t.score, t.result, t.created_at, u.username 
    FROM tests t 
    LEFT JOIN users u ON t.user_id = u.id 
    ORDER BY t.created_at DESC
");

// Get statistics
$today_tests = $con->query("SELECT COUNT(*) as count FROM tests WHERE DATE(created_at) = CURDATE()")->fetch_assoc()['count'];
$unique_users = $con->query("SELECT COUNT(DISTINCT user_id) as count FROM tests")->fetch_assoc()['count'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Management - Inspiremental</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .navbar-custom { background: linear-gradient(135deg, #667eea, #764ba2) !important; }
        .table th { background-color: #f8f9fa; }
        .badge-test { font-size: 0.8em; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-clipboard-list"></i> Test Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="user-management.php">
                    <i class="fas fa-users"></i> Users
                </a>
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home"></i> Main Site
                </a>
                <a class="nav-link" href="simple-login.php?logout=1">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1><i class="fas fa-clipboard-list"></i> Test Management</h1>
                        <p class="text-muted">Manage and monitor all psychological tests</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $result->num_rows; ?></h3>
                        <p>Total Tests</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $today_tests; ?></h3>
                        <p>Tests Today</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3><?php echo $unique_users; ?></h3>
                        <p>Tested Users</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tests Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> All Tests</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>User</th>
                                            <th>Test Type</th>
                                            <th>Score</th>
                                            <th>Result</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($test = $result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $test['id']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($test['username'] ?? 'Unknown'); ?>
                                                <small class="text-muted d-block">ID: <?php echo $test['user_id']; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge badge-test bg-primary">
                                                    <?php echo htmlspecialchars($test['test_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?php echo $test['score']; ?></strong>
                                            </td>
                                            <td>
                                                <?php
                                                $result_class = 'secondary';
                                                if (stripos($test['result'], 'normal') !== false) $result_class = 'success';
                                                elseif (stripos($test['result'], 'mild') !== false) $result_class = 'warning';
                                                elseif (stripos($test['result'], 'moderate') !== false) $result_class = 'danger';
                                                elseif (stripos($test['result'], 'severe') !== false) $result_class = 'dark';
                                                ?>
                                                <span class="badge bg-<?php echo $result_class; ?>">
                                                    <?php echo htmlspecialchars($test['result']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y H:i', strtotime($test['created_at'])); ?>
                                            </td>
                                            <td>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="test_id" value="<?php echo $test['id']; ?>">
                                                    <button type="submit" name="delete_test" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to delete this test?')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i>
                                No tests found
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
