<?php
/**
 * Debug file to test admin panel functionality
 * This file helps identify what's causing the 500 error
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔧 Admin Panel Debug</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #f0f0f8; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

echo "<div class='info'>";
echo "<h2>🔍 Testing Admin Panel Components</h2>";
echo "<p>This debug file tests each component that might be causing the 500 error.</p>";
echo "</div>";

// Test 1: Basic PHP
echo "<h3>1. Basic PHP Test</h3>";
echo "<p class='success'>✅ PHP is working (you can see this page)</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";

// Test 2: File existence
echo "<h3>2. File Existence Check</h3>";
$files_to_check = [
    '../db.php' => 'Database configuration',
    '../config.php' => 'Main configuration',
    '../includes/head.php' => 'Head include file',
    '../includes/script.php' => 'Script include file',
    '../includes/admin_navbar.php' => 'Admin navbar',
    'index.php' => 'Admin index'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description ($file) exists</p>";
    } else {
        echo "<p class='error'>❌ $description ($file) missing</p>";
    }
}

// Test 3: Include database
echo "<h3>3. Database Connection Test</h3>";
try {
    include '../db.php';
    echo "<p class='success'>✅ Database file included successfully</p>";
    
    if (isset($con) && $con instanceof mysqli) {
        echo "<p class='success'>✅ Database connection working</p>";
        echo "<p><strong>Environment:</strong> " . (defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Not defined') . "</p>";
        echo "<p><strong>Base URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error including database: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Include config
echo "<h3>4. Configuration Test</h3>";
try {
    include '../config.php';
    echo "<p class='success'>✅ Config file included successfully</p>";
    
    if (function_exists('assets_url')) {
        echo "<p class='success'>✅ URL helper functions available</p>";
        echo "<p><strong>Assets URL:</strong> " . assets_url('test') . "</p>";
    } else {
        echo "<p class='error'>❌ URL helper functions not available</p>";
    }
    
    if (defined('DEBUG_MODE')) {
        echo "<p class='info'>ℹ️ Debug mode: " . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error including config: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Session test
echo "<h3>5. Session Test</h3>";
try {
    session_start();
    echo "<p class='success'>✅ Session started successfully</p>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'>✅ User is logged in (ID: " . $_SESSION['user_id'] . ")</p>";
        echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
    } else {
        echo "<p class='warning'>⚠️ User is not logged in</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Session error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 6: Include test
echo "<h3>6. Include Files Test</h3>";
try {
    echo "<p>Testing head.php include...</p>";
    ob_start();
    include '../includes/head.php';
    $head_content = ob_get_clean();
    echo "<p class='success'>✅ Head include successful</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Head include error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

try {
    echo "<p>Testing admin navbar include...</p>";
    ob_start();
    include '../includes/admin_navbar.php';
    $navbar_content = ob_get_clean();
    echo "<p class='success'>✅ Admin navbar include successful</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Admin navbar include error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

try {
    echo "<p>Testing script include...</p>";
    ob_start();
    include '../includes/script.php';
    $script_content = ob_get_clean();
    echo "<p class='success'>✅ Script include successful</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Script include error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>7. Next Steps</h3>";
echo "<div class='info'>";
echo "<p>If all tests above pass, try accessing the admin panel:</p>";
echo "<p><a href='index.php' target='_blank'>🔗 Test Admin Panel</a></p>";
echo "<p>If the admin panel still shows a 500 error, check the server error logs for more details.</p>";
echo "</div>";

echo "<h3>8. Quick Fixes</h3>";
echo "<div class='warning'>";
echo "<p>If you need to quickly access the admin panel, you can:</p>";
echo "<ol>";
echo "<li>Check if you're logged in as an admin user</li>";
echo "<li>Clear your browser cache and cookies</li>";
echo "<li>Try accessing the admin panel in an incognito/private window</li>";
echo "</ol>";
echo "</div>";
?>
