<?php
/**
 * Temporary Login Bypass for Testing
 * DELETE AFTER FIXING ADMIN PANEL
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include config
include '../config.php';

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head><title>Login Bypass</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-warning'>";
echo "<h3>🚨 Temporary Login Bypass</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Check if admin user exists
try {
    $admin_result = $con->query("SELECT * FROM users WHERE role = 'Admin' LIMIT 1");
    if ($admin_result && $admin_result->num_rows > 0) {
        $admin_user = $admin_result->fetch_assoc();
        
        echo "<div class='alert alert-info'>";
        echo "<h5>Admin User Found:</h5>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($admin_user['username']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($admin_user['email']) . "</p>";
        echo "</div>";
        
        // Handle bypass request
        if (isset($_POST['bypass_login'])) {
            $_SESSION['user_id'] = $admin_user['id'];
            $_SESSION['username'] = $admin_user['username'];
            $_SESSION['role'] = $admin_user['role'];
            $_SESSION['credits'] = $admin_user['credits'];
            
            echo "<div class='alert alert-success'>";
            echo "<h5>✅ Login Bypass Successful!</h5>";
            echo "<p>You are now logged in as: " . $admin_user['username'] . "</p>";
            echo "<p><a href='index.php' class='btn btn-success'>Access Admin Panel</a></p>";
            echo "</div>";
        } else {
            echo "<form method='post'>";
            echo "<div class='alert alert-warning'>";
            echo "<h5>⚠️ Warning</h5>";
            echo "<p>This will log you in as the admin user without password verification.</p>";
            echo "<p>Use only for debugging purposes!</p>";
            echo "</div>";
            echo "<button type='submit' name='bypass_login' class='btn btn-warning'>Bypass Login</button>";
            echo "</form>";
        }
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ No Admin User Found</h5>";
        echo "<p>No admin users exist in the database.</p>";
        echo "<p><a href='../create-admin.php' class='btn btn-primary'>Create Admin User</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ Database Error</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Current session status
echo "<div class='mt-4'>";
echo "<h5>Current Session Status:</h5>";
if (isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-success'>";
    echo "<p><strong>Logged in as:</strong> " . ($_SESSION['username'] ?? 'Unknown') . "</p>";
    echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Unknown') . "</p>";
    echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<p>No active session</p>";
    echo "</div>";
}
echo "</div>";

// Quick actions
echo "<div class='mt-4'>";
echo "<h5>Quick Actions:</h5>";
echo "<a href='index.php' class='btn btn-primary me-2'>Admin Panel</a>";
echo "<a href='../login.php' class='btn btn-secondary me-2'>Normal Login</a>";
echo "<a href='../logout.php' class='btn btn-outline-danger'>Logout</a>";
echo "</div>";

echo "<div class='alert alert-danger mt-4'>";
echo "<h5>🚨 Security Warning</h5>";
echo "<p><strong>DELETE THIS FILE AFTER TESTING!</strong></p>";
echo "<p>This file bypasses authentication and is a security risk.</p>";
echo "</div>";

echo "</div>"; // card-body
echo "</div>"; // card
echo "</div>"; // col
echo "</div>"; // row
echo "</div>"; // container

echo "</body>";
echo "</html>";
?>
