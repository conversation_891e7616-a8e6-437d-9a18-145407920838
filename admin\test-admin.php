<?php
/**
 * Minimal Admin Test
 * Tests admin panel step by step
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Admin Panel Step-by-Step Test</h1>";

// Step 1: Basic PHP
echo "<h2>Step 1: Basic PHP ✅</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Step 2: Include config (which includes db)
echo "<h2>Step 2: Include Config</h2>";
try {
    include '../config.php';
    echo "<p style='color: green;'>✅ Config included successfully</p>";
    echo "<p>Environment: " . (defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Not defined') . "</p>";
    echo "<p>Base URL: " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config error: " . $e->getMessage() . "</p>";
    exit;
}

// Step 3: Test database connection
echo "<h2>Step 3: Database Connection</h2>";
if (isset($con)) {
    echo "<p style='color: green;'>✅ Database connection available</p>";
    
    try {
        $user_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
        echo "<p>Total users: $user_count</p>";
        
        $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
        echo "<p>Admin users: $admin_count</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database query error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Database connection not available</p>";
}

// Step 4: Start session
echo "<h2>Step 4: Session Test</h2>";
try {
    session_start();
    echo "<p style='color: green;'>✅ Session started</p>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p style='color: green;'>✅ User logged in</p>";
        echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
        echo "<p>Username: " . ($_SESSION['username'] ?? 'Not set') . "</p>";
        echo "<p>Role: " . ($_SESSION['role'] ?? 'Not set') . "</p>";
        
        if ($_SESSION['role'] === 'Admin') {
            echo "<p style='color: green;'>✅ Admin access granted</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ User is not an admin</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No user session - need to login</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Session error: " . $e->getMessage() . "</p>";
}

// Step 5: Test includes
echo "<h2>Step 5: Include Test</h2>";

$includes = [
    '../includes/head.php' => 'Head include',
    '../includes/admin_navbar.php' => 'Admin navbar',
    '../includes/script.php' => 'Script include'
];

foreach ($includes as $file => $description) {
    echo "<h4>Testing: $description</h4>";
    if (file_exists($file)) {
        try {
            ob_start();
            include $file;
            $content = ob_get_clean();
            echo "<p style='color: green;'>✅ $description included successfully</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error including $description: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ $file does not exist</p>";
    }
}

// Step 6: Authentication check simulation
echo "<h2>Step 6: Authentication Logic Test</h2>";

if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: orange;'>⚠️ Would redirect to login page</p>";
    echo "<p>Redirect URL: ../login.php</p>";
} elseif ($_SESSION['role'] !== 'Admin') {
    echo "<p style='color: orange;'>⚠️ Would redirect to home page</p>";
    echo "<p>Redirect URL: ../index.php?page=home</p>";
} else {
    echo "<p style='color: green;'>✅ Authentication passed - admin access granted</p>";
}

// Quick actions
echo "<h2>Quick Actions</h2>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none;'>Try Full Admin Panel</a></p>";
echo "<p><a href='../login.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none;'>Login Page</a></p>";
echo "<p><a href='../index.php' style='background: #6c757d; color: white; padding: 10px; text-decoration: none;'>Main Site</a></p>";

echo "<div style='background: #d1ecf1; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>Summary</h3>";
echo "<p>This test shows each step of the admin panel loading process.</p>";
echo "<p>If all steps pass but the full admin panel still fails, the issue is likely in the HTML/CSS rendering or specific admin panel logic.</p>";
echo "</div>";
?>
