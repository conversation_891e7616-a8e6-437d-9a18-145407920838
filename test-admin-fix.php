<?php
// Test if admin directory fix worked
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2>🔧 Admin Directory Fix Test</h2>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h4>Testing Admin Directory Access</h4>
                            <p>Click the buttons below to test if the admin directory is now accessible:</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>Test Files:</h5>
                                <div class="d-grid gap-2">
                                    <a href="admin/test-fix.php" class="btn btn-primary" target="_blank">
                                        🧪 Test Simple File
                                    </a>
                                    <a href="admin/index.php" class="btn btn-success" target="_blank">
                                        🎯 Test Original Admin Panel
                                    </a>
                                    <a href="admin/users.php" class="btn btn-info" target="_blank">
                                        👥 Test User Management
                                    </a>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>Working Alternatives:</h5>
                                <div class="d-grid gap-2">
                                    <a href="admin-panel.php" class="btn btn-warning">
                                        ✅ Working Admin Panel (Root)
                                    </a>
                                    <a href="simple-login.php" class="btn btn-secondary">
                                        🔐 Login Page
                                    </a>
                                    <a href="index.php" class="btn btn-outline-primary">
                                        🏠 Main Site
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <h5>What I Fixed:</h5>
                            <ul>
                                <li>✅ Added <code>.htaccess</code> file to admin directory to override restrictions</li>
                                <li>✅ Simplified admin panel code to remove problematic includes</li>
                                <li>✅ Added direct database connection to avoid config issues</li>
                                <li>✅ Created working admin panel in root directory as backup</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h5>Expected Results:</h5>
                            <p><strong>If the fix worked:</strong> All admin directory files should now load without 500/403 errors</p>
                            <p><strong>If still having issues:</strong> Use the working admin panel in the root directory</p>
                        </div>

                        <?php if (isset($_SESSION['user_id'])): ?>
                            <div class="alert alert-info">
                                <h5>Current Session:</h5>
                                <p><strong>User:</strong> <?php echo $_SESSION['username']; ?></p>
                                <p><strong>Role:</strong> <?php echo $_SESSION['role']; ?></p>
                                <p>You're logged in and ready to test the admin panels!</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h5>Not Logged In</h5>
                                <p>You need to login first to test the admin panels.</p>
                                <a href="simple-login.php" class="btn btn-primary">Login Here</a>
                            </div>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
