<?php
/**
 * Session Test - Clean session testing without header issues
 */

// Start session FIRST - before any output
session_start();

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include config for database access
try {
    include 'config.php';
    $config_loaded = true;
} catch (Exception $e) {
    $config_loaded = false;
    $config_error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2>🔐 Session Test</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- Session Status -->
                        <div class="alert alert-success">
                            <h4>✅ Session Working!</h4>
                            <p>Session started successfully without header errors.</p>
                            <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                        </div>

                        <!-- Current Session Data -->
                        <h4>Current Session Data:</h4>
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <div class="alert alert-info">
                                <h5>✅ User Logged In</h5>
                                <table class="table table-sm">
                                    <tr><th>User ID</th><td><?php echo $_SESSION['user_id']; ?></td></tr>
                                    <tr><th>Username</th><td><?php echo $_SESSION['username'] ?? 'Not set'; ?></td></tr>
                                    <tr><th>Role</th><td><?php echo $_SESSION['role'] ?? 'Not set'; ?></td></tr>
                                    <tr><th>Credits</th><td><?php echo $_SESSION['credits'] ?? 'Not set'; ?></td></tr>
                                </table>
                                
                                <?php if ($_SESSION['role'] === 'Admin'): ?>
                                    <div class="alert alert-success mt-2">
                                        <strong>✅ Admin Access Granted!</strong>
                                        <p>You can access the admin panel.</p>
                                        <a href="admin/index.php" class="btn btn-success">Go to Admin Panel</a>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning mt-2">
                                        <strong>⚠️ Not an Admin</strong>
                                        <p>You need admin privileges to access the admin panel.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h5>⚠️ No User Session</h5>
                                <p>You are not logged in.</p>
                            </div>
                        <?php endif; ?>

                        <!-- Database Status -->
                        <h4>Database Status:</h4>
                        <?php if ($config_loaded): ?>
                            <div class="alert alert-success">
                                <h5>✅ Database Connected</h5>
                                <p><strong>Environment:</strong> <?php echo defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Unknown'; ?></p>
                                <p><strong>Base URL:</strong> <?php echo defined('BASE_URL') ? BASE_URL : 'Unknown'; ?></p>
                                
                                <?php
                                try {
                                    $user_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
                                    $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
                                    echo "<p><strong>Total Users:</strong> $user_count</p>";
                                    echo "<p><strong>Admin Users:</strong> $admin_count</p>";
                                } catch (Exception $e) {
                                    echo "<p class='text-danger'>Database query error: " . htmlspecialchars($e->getMessage()) . "</p>";
                                }
                                ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h5>❌ Database Error</h5>
                                <p><?php echo htmlspecialchars($config_error); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Quick Actions -->
                        <h4>Quick Actions:</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Access:</h6>
                                <a href="admin/index.php" class="btn btn-primary btn-sm d-block mb-1">Admin Panel</a>
                                <a href="login.php" class="btn btn-secondary btn-sm d-block mb-1">Login Page</a>
                                <a href="logout.php" class="btn btn-outline-danger btn-sm d-block mb-1">Logout</a>
                            </div>
                            <div class="col-md-6">
                                <h6>Testing:</h6>
                                <a href="admin/login-bypass.php" class="btn btn-warning btn-sm d-block mb-1">Login Bypass</a>
                                <a href="fix-admin.php" class="btn btn-info btn-sm d-block mb-1">Fix Admin</a>
                                <a href="create-admin.php" class="btn btn-outline-primary btn-sm d-block mb-1">Create Admin</a>
                            </div>
                        </div>

                        <!-- Session Management -->
                        <h4>Session Management:</h4>
                        <div class="alert alert-light">
                            <p>Test session functionality:</p>
                            <form method="post" class="d-inline">
                                <button type="submit" name="test_session" class="btn btn-outline-primary btn-sm">Set Test Session</button>
                            </form>
                            <form method="post" class="d-inline">
                                <button type="submit" name="clear_session" class="btn btn-outline-warning btn-sm">Clear Session</button>
                            </form>
                            
                            <?php
                            if (isset($_POST['test_session'])) {
                                $_SESSION['test_data'] = 'Session test at ' . date('Y-m-d H:i:s');
                                echo "<div class='alert alert-success mt-2'>✅ Test session data set!</div>";
                            }
                            
                            if (isset($_POST['clear_session'])) {
                                session_unset();
                                session_destroy();
                                echo "<div class='alert alert-info mt-2'>ℹ️ Session cleared! <a href='session-test.php'>Refresh page</a></div>";
                            }
                            
                            if (isset($_SESSION['test_data'])) {
                                echo "<p class='mt-2'><strong>Test Data:</strong> " . $_SESSION['test_data'] . "</p>";
                            }
                            ?>
                        </div>

                        <!-- System Info -->
                        <h4>System Information:</h4>
                        <div class="alert alert-light">
                            <small>
                                <strong>PHP Version:</strong> <?php echo phpversion(); ?><br>
                                <strong>Server:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Unknown'; ?><br>
                                <strong>Script:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?><br>
                                <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                            </small>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
