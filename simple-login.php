<?php
// Super simple login - no includes, no complications
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
} else {
    $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
}

$con = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($con->connect_error) die("Database failed: " . $con->connect_error);

// Handle login
if ($_POST['email'] ?? false) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    $stmt = $con->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['credits'] = $user['credits'];
        $success = "Login successful!";
    } else {
        $error = "Invalid credentials";
    }
}

// Handle logout
if ($_GET['logout'] ?? false) {
    session_destroy();
    header("Location: simple-login.php");
    exit();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h2>🔐 Simple Login</h2>
                    </div>
                    <div class="card-body">
                        
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <!-- Logged in -->
                            <div class="alert alert-success">
                                <h4>✅ Logged In!</h4>
                                <p><strong>User:</strong> <?php echo $_SESSION['username']; ?></p>
                                <p><strong>Role:</strong> <?php echo $_SESSION['role']; ?></p>
                            </div>
                            
                            <?php if ($_SESSION['role'] === 'Admin'): ?>
                                <div class="d-grid gap-2">
                                    <a href="admin/ultra-simple.php" class="btn btn-warning btn-lg">
                                        🧪 Test Ultra Simple (Try This First)
                                    </a>
                                    <a href="admin/working-admin.php" class="btn btn-success btn-lg">
                                        🚀 Go to Working Admin Panel
                                    </a>
                                    <a href="admin/index.php" class="btn btn-primary">
                                        Try Original Admin Panel
                                    </a>
                                    <a href="?logout=1" class="btn btn-outline-danger">Logout</a>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <h6>🔍 Debugging Steps:</h6>
                                    <ol>
                                        <li>Try "Test Ultra Simple" first</li>
                                        <li>If that works, try "Working Admin Panel"</li>
                                        <li>If you get 500 errors, check server error logs</li>
                                    </ol>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">Not an admin user</div>
                                <a href="?logout=1" class="btn btn-outline-danger">Logout</a>
                            <?php endif; ?>
                            
                        <?php else: ?>
                            <!-- Login form -->
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger"><?php echo $error; ?></div>
                            <?php endif; ?>
                            
                            <?php if (isset($success)): ?>
                                <div class="alert alert-success"><?php echo $success; ?></div>
                                <script>setTimeout(() => location.reload(), 1000);</script>
                            <?php endif; ?>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">Email:</label>
                                    <input type="email" name="email" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Password:</label>
                                    <input type="password" name="password" class="form-control" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </form>
                            
                            <hr>
                            <h6>Available Admin Users:</h6>
                            <?php
                            $admins = $con->query("SELECT username, email FROM users WHERE role = 'Admin'");
                            while ($admin = $admins->fetch_assoc()) {
                                echo "<small><strong>" . $admin['username'] . "</strong> - " . $admin['email'] . "</small><br>";
                            }
                            ?>
                            
                        <?php endif; ?>
                        
                    </div>
                </div>
                
                <!-- Status -->
                <div class="card mt-3">
                    <div class="card-body">
                        <small>
                            <strong>Database:</strong> ✅ Connected<br>
                            <strong>Session:</strong> ✅ Active<br>
                            <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
