<?php
// Error checker - shows exactly what's wrong
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<!DOCTYPE html><html><head><title>Error Check</title></head><body>";
echo "<h1>🔍 Error Diagnostic</h1>";

// Test 1: Basic PHP
echo "<h2>1. Basic PHP Test</h2>";
echo "<p style='color: green;'>✅ PHP " . phpversion() . " is working</p>";

// Test 2: Session
echo "<h2>2. Session Test</h2>";
try {
    session_start();
    echo "<p style='color: green;'>✅ Session started</p>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p style='color: green;'>✅ User logged in: " . $_SESSION['username'] . "</p>";
        echo "<p style='color: green;'>✅ Role: " . $_SESSION['role'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ No user session</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Session error: " . $e->getMessage() . "</p>";
}

// Test 3: Database
echo "<h2>3. Database Test</h2>";
try {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    if (strpos($host, 'localhost') !== false) {
        $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
    } else {
        $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
    }
    
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $con->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Database connected</p>";
        
        $result = $con->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ Database query works - $count users found</p>";
        } else {
            echo "<p style='color: red;'>❌ Database query failed: " . $con->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: File permissions
echo "<h2>4. File System Test</h2>";
$current_file = __FILE__;
echo "<p><strong>Current file:</strong> $current_file</p>";
echo "<p><strong>File readable:</strong> " . (is_readable($current_file) ? 'Yes' : 'No') . "</p>";
echo "<p><strong>Directory writable:</strong> " . (is_writable(dirname($current_file)) ? 'Yes' : 'No') . "</p>";

// Test 5: Server info
echo "<h2>5. Server Information</h2>";
echo "<p><strong>Server:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>PHP Max Execution Time:</strong> " . ini_get('max_execution_time') . "</p>";

// Test 6: Error log check
echo "<h2>6. Recent Errors</h2>";
$error_log_file = '../error_log';
if (file_exists($error_log_file)) {
    $errors = file_get_contents($error_log_file);
    $lines = explode("\n", $errors);
    $recent = array_slice($lines, -5);
    echo "<pre style='background: #f0f0f0; padding: 10px; font-size: 12px;'>";
    foreach ($recent as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>No error log file found</p>";
}

// Test 7: Simple admin panel test
echo "<h2>7. Admin Panel Test</h2>";
if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'Admin') {
    echo "<p style='color: green;'>✅ You have admin access</p>";
    echo "<p><strong>Next step:</strong> The admin panel should work</p>";
    
    // Test if we can create a simple admin interface right here
    echo "<h3>Inline Admin Test:</h3>";
    if (isset($con)) {
        try {
            $users = $con->query("SELECT id, username, email, role FROM users LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th></tr>";
            while ($user = $users->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p style='color: green;'>✅ Admin functionality works inline!</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Admin query failed: " . $e->getMessage() . "</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ No admin access</p>";
}

echo "<h2>8. Conclusion</h2>";
echo "<p>If all tests above pass, the issue might be:</p>";
echo "<ul>";
echo "<li>Server configuration blocking certain files</li>";
echo "<li>File permissions on specific admin files</li>";
echo "<li>Memory or execution time limits</li>";
echo "<li>Specific PHP extensions missing</li>";
echo "</ul>";

echo "<p><a href='../simple-login.php'>Back to Login</a></p>";
echo "</body></html>";
?>
