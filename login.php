<?php
session_start();
include 'db.php';
include 'config.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = $_POST['email'];
    $password = $_POST['password'];
    $rememberMe = isset($_POST['rememberMe']); // Check if "Remember Me" is selected

    $stmt = $con->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if ($user && password_verify($password, $user['password'])) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['credits'] = $user['credits'];

        if ($rememberMe) {
            // Generate a unique token and set the cookie
            $rememberToken = bin2hex(random_bytes(16));
            $expiry = time() + (30 * 24 * 60 * 60); // <PERSON>ie expires in 30 days

            setcookie("remember_me", $rememberToken, $expiry, "/"); // Set the cookie

            // Store the token and its expiry in the database
            $updateTokenQuery = "UPDATE users SET remember_me_token = ?, remember_me_expiry = ? WHERE id = ?";
            $stmt = $con->prepare($updateTokenQuery);
            $expiryDate = date('Y-m-d H:i:s', $expiry);
            $stmt->bind_param("ssi", $rememberToken, $expiryDate, $user['id']);
            $stmt->execute();
        }

        // Return a JSON object with the redirect location
        echo json_encode([
            'success' => true,
            'redirect' => ($user['role'] == 'Admin') ? 'management' : 'home'
        ]);
    } else {
        // Return a JSON object indicating failure
        echo json_encode([
            'success' => false,
            'message' => 'Invalid email or password'
        ]);
    }
    exit(); // Ensure no further output is sent
}
?>
