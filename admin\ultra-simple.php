<?php
// Ultra simple test - just basic HTML and PHP
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Ultra Simple Test</title></head><body>";
echo "<h1>Ultra Simple Admin Test</h1>";
echo "<p>PHP is working: " . phpversion() . "</p>";
echo "<p>Time: " . date('Y-m-d H:i:s') . "</p>";

if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✅ Session working - User: " . $_SESSION['username'] . "</p>";
    echo "<p style='color: green;'>✅ Role: " . $_SESSION['role'] . "</p>";
    
    if ($_SESSION['role'] === 'Admin') {
        echo "<p style='color: green;'>✅ Admin access confirmed</p>";
        echo "<h2>This proves the admin panel CAN work!</h2>";
    }
} else {
    echo "<p style='color: red;'>❌ No session found</p>";
}

echo "<p><a href='../simple-login.php'>Back to Login</a></p>";
echo "</body></html>";
?>
