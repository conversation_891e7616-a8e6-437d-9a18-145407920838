<?php
// Admin panel in root directory - bypasses admin folder restrictions
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
} else {
    $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) die("Database failed: " . $con->connect_error);
    $con->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: simple-login.php');
    exit();
}

// Handle user actions
$message = '';
if ($_POST['action'] ?? false) {
    $action = $_POST['action'];
    $user_id = (int)($_POST['user_id'] ?? 0);
    
    switch ($action) {
        case 'delete_user':
            if ($user_id != $_SESSION['user_id']) {
                $con->query("DELETE FROM users WHERE id = $user_id");
                $message = "User deleted successfully";
            }
            break;
        case 'make_admin':
            $con->query("UPDATE users SET role = 'Admin' WHERE id = $user_id");
            $message = "User promoted to admin";
            break;
        case 'remove_admin':
            if ($user_id != $_SESSION['user_id']) {
                $con->query("UPDATE users SET role = 'User' WHERE id = $user_id");
                $message = "Admin privileges removed";
            }
            break;
    }
}

// Get statistics
$stats = [
    'users' => $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'],
    'admins' => $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'],
    'tests' => $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'] ?? 0
];

// Get users
$users = $con->query("SELECT * FROM users ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Working</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .admin-container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .stat-card { background: linear-gradient(135deg, #28a745, #20c997); color: white; text-align: center; padding: 2rem; border-radius: 15px; margin-bottom: 1rem; }
        .stat-number { font-size: 2.5rem; font-weight: bold; display: block; }
        .navbar-custom { background: linear-gradient(135deg, #667eea, #764ba2) !important; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="admin-panel.php">
                <i class="fas fa-tachometer-alt"></i> Admin Panel (Working)
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>
                </span>
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home"></i> Main Site
                </a>
                <a class="nav-link" href="simple-login.php?logout=1">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="admin-container">
        <div class="container">
            
            <!-- Success Message -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Success Banner -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h1><i class="fas fa-check-circle"></i> Admin Panel is Working!</h1>
                            <p class="mb-0">Successfully bypassed the /admin/ directory restrictions</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $stats['users']; ?></span>
                        <span>Total Users</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $stats['admins']; ?></span>
                        <span>Admin Users</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $stats['tests']; ?></span>
                        <span>Tests</span>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-users"></i> User Management</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Credits</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $users->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-primary">You</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['role'] === 'Admin' ? 'danger' : 'secondary'; ?>">
                                                    <?php echo $user['role']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $user['credits'] ?? 0; ?></td>
                                            <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if ($user['role'] !== 'Admin'): ?>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="action" value="make_admin">
                                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                                <button type="submit" class="btn btn-success" onclick="return confirm('Make admin?')">
                                                                    <i class="fas fa-user-shield"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <form method="post" class="d-inline">
                                                                <input type="hidden" name="action" value="remove_admin">
                                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                                <button type="submit" class="btn btn-warning" onclick="return confirm('Remove admin?')">
                                                                    <i class="fas fa-user-minus"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        <form method="post" class="d-inline">
                                                            <input type="hidden" name="action" value="delete_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <button type="submit" class="btn btn-danger" onclick="return confirm('Delete user?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">Current User</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> System Status</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Database:</strong></td><td><span class="text-success">✅ Connected</span></td></tr>
                                <tr><td><strong>Session:</strong></td><td><span class="text-success">✅ Active</span></td></tr>
                                <tr><td><strong>PHP Version:</strong></td><td><?php echo phpversion(); ?></td></tr>
                                <tr><td><strong>Server:</strong></td><td><?php echo $_SERVER['HTTP_HOST']; ?></td></tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools"></i> Admin Tools</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="create-admin.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Create New Admin
                                </a>
                                <button class="btn btn-info" onclick="location.reload()">
                                    <i class="fas fa-sync"></i> Refresh Data
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-home"></i> Back to Main Site
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Problem Explanation -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-warning">
                        <div class="card-header bg-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> Why This Works</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Problem:</strong> Your server has security restrictions on the <code>/admin/</code> directory that block PHP execution.</p>
                            <p><strong>Solution:</strong> This admin panel is located in the root directory (<code>admin-panel.php</code>) which bypasses those restrictions.</p>
                            <p><strong>Result:</strong> Full admin functionality without server permission issues!</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
