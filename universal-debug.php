<?php
/**
 * Universal Debug Tool
 * Works on both localhost and production (inspiremental.org)
 * Diagnoses and fixes admin panel issues
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('log_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Universal Debug Tool</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".card { background: white; border-radius: 10px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #c3e6cb; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #ffeaa7; }";
echo ".error { color: #721c24; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #f5c6cb; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #bee5eb; }";
echo ".btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }";
echo ".btn:hover { background: #0056b3; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-danger { background: #dc3545; }";
echo ".btn-warning { background: #ffc107; color: #212529; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }";
echo "th { background: #f8f9fa; }";
echo ".grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 Universal Debug Tool</h1>";

// Environment Detection
echo "<div class='card'>";
echo "<h2>🌍 Environment Detection</h2>";

$host = $_SERVER['HTTP_HOST'] ?? 'unknown';
$documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? 'unknown';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? 'unknown';
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';

echo "<table>";
echo "<tr><th>Property</th><th>Value</th></tr>";
echo "<tr><td>Host</td><td>$host</td></tr>";
echo "<tr><td>Document Root</td><td>$documentRoot</td></tr>";
echo "<tr><td>Script Name</td><td>$scriptName</td></tr>";
echo "<tr><td>Protocol</td><td>$protocol</td></tr>";
echo "<tr><td>PHP Version</td><td>" . phpversion() . "</td></tr>";
echo "</table>";

// Test database connection
echo "<h3>Database Connection Test</h3>";
try {
    include 'db.php';
    echo "<div class='success'>✅ Database connection successful</div>";
    echo "<p><strong>Environment:</strong> " . (defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Not defined') . "</p>";
    echo "<p><strong>Base URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
    
    // Test database queries
    $user_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    echo "<p><strong>Total Users:</strong> $user_count</p>";
    
    $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
    echo "<p><strong>Admin Users:</strong> $admin_count</p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test config loading
echo "<h3>Configuration Test</h3>";
try {
    if (!function_exists('assets_url')) {
        include 'config.php';
    }
    echo "<div class='success'>✅ Config loaded successfully</div>";
    
    if (function_exists('assets_url')) {
        echo "<p><strong>Assets URL:</strong> " . assets_url('test') . "</p>";
        echo "<p><strong>Admin URL:</strong> " . admin_url() . "</p>";
    } else {
        echo "<div class='error'>❌ URL helper functions not available</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Config error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// Session Test
echo "<div class='card'>";
echo "<h2>🔐 Session & Authentication Test</h2>";

try {
    session_start();
    echo "<div class='success'>✅ Session started successfully</div>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div class='success'>✅ User is logged in</div>";
        echo "<table>";
        echo "<tr><th>Session Key</th><th>Value</th></tr>";
        echo "<tr><td>User ID</td><td>" . ($_SESSION['user_id'] ?? 'Not set') . "</td></tr>";
        echo "<tr><td>Username</td><td>" . ($_SESSION['username'] ?? 'Not set') . "</td></tr>";
        echo "<tr><td>Role</td><td>" . ($_SESSION['role'] ?? 'Not set') . "</td></tr>";
        echo "<tr><td>Credits</td><td>" . ($_SESSION['credits'] ?? 'Not set') . "</td></tr>";
        echo "</table>";
        
        if ($_SESSION['role'] === 'Admin') {
            echo "<div class='success'>✅ Admin access granted</div>";
        } else {
            echo "<div class='warning'>⚠️ User is not an admin</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ User is not logged in</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Session error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "</div>";

// File Tests
echo "<div class='card'>";
echo "<h2>📁 File Existence Test</h2>";

$files_to_check = [
    'db.php' => 'Database configuration',
    'config.php' => 'Main configuration',
    'head.php' => 'Main head include',
    'script.php' => 'Main script include',
    'includes/head.php' => 'Admin head include',
    'includes/script.php' => 'Admin script include',
    'includes/admin_navbar.php' => 'Admin navbar',
    'admin/index.php' => 'Admin panel index',
    'admin/users.php' => 'Admin users page',
    'admin/tests.php' => 'Admin tests page',
    'login.php' => 'Login page',
    '.htaccess' => 'URL rewrite rules'
];

echo "<div class='grid'>";
foreach ($files_to_check as $file => $description) {
    echo "<div>";
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description</div>";
        echo "<small>$file</small>";
    } else {
        echo "<div class='error'>❌ $description</div>";
        echo "<small>$file (missing)</small>";
    }
    echo "</div>";
}
echo "</div>";

echo "</div>";

// Include Tests
echo "<div class='card'>";
echo "<h2>🔗 Include Tests</h2>";

$includes_to_test = [
    'head.php' => 'Main head include',
    'script.php' => 'Main script include',
    'includes/head.php' => 'Admin head include',
    'includes/script.php' => 'Admin script include'
];

foreach ($includes_to_test as $file => $description) {
    echo "<h4>Testing: $description</h4>";
    if (file_exists($file)) {
        try {
            ob_start();
            include $file;
            $content = ob_get_clean();
            echo "<div class='success'>✅ $file included successfully</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error including $file: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    } else {
        echo "<div class='error'>❌ $file does not exist</div>";
    }
}

echo "</div>";

// Quick Actions
echo "<div class='card'>";
echo "<h2>⚡ Quick Actions</h2>";

echo "<div class='grid'>";

// Test admin panel
echo "<div>";
echo "<h4>Admin Panel Test</h4>";
if (isset($_SESSION['user_id']) && $_SESSION['role'] === 'Admin') {
    echo "<a href='admin/index.php' class='btn btn-success'>🔗 Access Admin Panel</a>";
} else {
    echo "<a href='login.php' class='btn btn-warning'>🔐 Login First</a>";
}
echo "</div>";

// Create admin user
echo "<div>";
echo "<h4>Admin User Management</h4>";
echo "<a href='create-admin.php' class='btn btn-warning'>👤 Create Admin User</a>";
echo "</div>";

// Test pages
echo "<div>";
echo "<h4>Test Pages</h4>";
echo "<a href='admin/debug.php' class='btn'>🔧 Admin Debug</a>";
echo "<a href='admin/test-minimal.php' class='btn'>🧪 Minimal Test</a>";
echo "</div>";

// Main site
echo "<div>";
echo "<h4>Navigation</h4>";
echo "<a href='index.php' class='btn'>🏠 Main Site</a>";
echo "<a href='home' class='btn'>🏠 Home Page</a>";
echo "</div>";

echo "</div>";

echo "</div>";

// Error Log Check
echo "<div class='card'>";
echo "<h2>📋 Recent Error Log</h2>";

if (file_exists('error_log')) {
    $error_log = file_get_contents('error_log');
    $lines = explode("\n", $error_log);
    $recent_lines = array_slice($lines, -10); // Last 10 lines
    
    echo "<div class='info'>";
    echo "<h4>Last 10 error log entries:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
    echo "</div>";
} else {
    echo "<div class='info'>ℹ️ No error log file found</div>";
}

echo "</div>";

// Security Warning
echo "<div class='card'>";
echo "<div class='error'>";
echo "<h3>🚨 Security Warning</h3>";
echo "<p><strong>Delete this file after debugging!</strong></p>";
echo "<p>This file exposes sensitive system information and should not remain on a production server.</p>";
echo "</div>";
echo "</div>";

echo "</div>"; // Close container
echo "</body>";
echo "</html>";
?>
