<?php
// Bulletproof admin panel - minimal code, maximum reliability
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection without includes
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    // Localhost
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'inspiremental';
} else {
    // Production
    $db_host = 'localhost';
    $db_user = 'yaridagr_inspiremental';
    $db_pass = 'n*dMFX=i0-iq';
    $db_name = 'yaridagr_inspiremental';
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) {
        die("Database connection failed: " . $con->connect_error);
    }
    $con->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    echo "<!DOCTYPE html><html><head><title>Access Denied</title></head><body>";
    echo "<h1>Access Denied</h1>";
    echo "<p>You need to be logged in as an admin.</p>";
    echo "<p><a href='../admin-login.php'>Login Here</a></p>";
    echo "</body></html>";
    exit();
}

// Get statistics
$users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];

// Handle user management
if (isset($_POST['delete_user'])) {
    $user_id = (int)$_POST['user_id'];
    if ($user_id != $_SESSION['user_id']) { // Don't delete yourself
        $con->query("DELETE FROM users WHERE id = $user_id");
        $message = "User deleted successfully";
    }
}

if (isset($_POST['make_admin'])) {
    $user_id = (int)$_POST['user_id'];
    $con->query("UPDATE users SET role = 'Admin' WHERE id = $user_id");
    $message = "User promoted to admin";
}

// Get users
$users = $con->query("SELECT * FROM users ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Working Admin Panel</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .admin-header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 1rem 0; }
        .stat-card { background: linear-gradient(135deg, #28a745, #20c997); color: white; border-radius: 10px; padding: 1.5rem; margin: 0.5rem 0; }
        .stat-number { font-size: 2rem; font-weight: bold; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1><i class="fas fa-tachometer-alt"></i> Working Admin Panel</h1>
                    <p class="mb-0">Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?></p>
                </div>
                <div class="col-auto">
                    <a href="../admin-login.php?logout=1" class="btn btn-light">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        
        <?php if (isset($message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row">
            <div class="col-md-6">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $users_count; ?></div>
                    <div>Total Users</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card text-center">
                    <div class="stat-number"><?php echo $admin_count; ?></div>
                    <div>Admin Users</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <a href="../create-admin.php" class="btn btn-primary me-2">
                            <i class="fas fa-user-plus"></i> Create Admin User
                        </a>
                        <a href="../index.php" class="btn btn-secondary me-2">
                            <i class="fas fa-home"></i> Main Site
                        </a>
                        <a href="users.php" class="btn btn-info me-2">
                            <i class="fas fa-users"></i> User Management
                        </a>
                        <a href="tests.php" class="btn btn-warning">
                            <i class="fas fa-clipboard-list"></i> Test Management
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-users"></i> User Management</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($user = $users->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['role'] === 'Admin' ? 'danger' : 'primary'; ?>">
                                                <?php echo $user['role']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $user['created_at']; ?></td>
                                        <td>
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <?php if ($user['role'] !== 'Admin'): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <button type="submit" name="make_admin" class="btn btn-sm btn-success" 
                                                                onclick="return confirm('Make this user an admin?')">
                                                            <i class="fas fa-user-shield"></i> Make Admin
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" name="delete_user" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Delete this user?')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-muted">Current User</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> System Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr><td><strong>PHP Version:</strong></td><td><?php echo phpversion(); ?></td></tr>
                                    <tr><td><strong>Server:</strong></td><td><?php echo $_SERVER['HTTP_HOST']; ?></td></tr>
                                    <tr><td><strong>Database:</strong></td><td>Connected ✅</td></tr>
                                    <tr><td><strong>Session:</strong></td><td>Active ✅</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr><td><strong>Current User:</strong></td><td><?php echo $_SESSION['username']; ?></td></tr>
                                    <tr><td><strong>User ID:</strong></td><td><?php echo $_SESSION['user_id']; ?></td></tr>
                                    <tr><td><strong>Role:</strong></td><td><?php echo $_SESSION['role']; ?></td></tr>
                                    <tr><td><strong>Time:</strong></td><td><?php echo date('Y-m-d H:i:s'); ?></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
