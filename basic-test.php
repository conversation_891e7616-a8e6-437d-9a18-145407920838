<?php
// Ultra basic test - just PHP info
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Basic PHP Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Server: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";

// Test if we can connect to database with hardcoded values
echo "<h2>Database Test</h2>";

// Detect environment
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
    // Localhost
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'inspiremental';
    echo "<p>Environment: Localhost</p>";
} else {
    // Production
    $db_host = 'localhost';
    $db_user = 'yaridagr_inspiremental';
    $db_pass = 'n*dMFX=i0-iq';
    $db_name = 'yaridagr_inspiremental';
    echo "<p>Environment: Production</p>";
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) {
        echo "<p style='color: red;'>Database Error: " . $con->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>Database Connected!</p>";
        $result = $con->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p>Total users: " . $row['count'] . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>File Check</h2>";
$files = ['db.php', 'config.php', 'admin/index.php'];
foreach ($files as $file) {
    $exists = file_exists($file) ? 'YES' : 'NO';
    echo "<p>$file: $exists</p>";
}

echo "<p><a href='admin/index.php'>Try Admin Panel</a></p>";
?>
