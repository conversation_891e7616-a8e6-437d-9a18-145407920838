<?php
/**
 * Ultra Simple Test - No includes, just basic PHP
 * This should work even if everything else is broken
 */

// Start session first
session_start();

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head><title>Simple Test</title></head>";
echo "<body style='font-family: Arial; margin: 20px; background: #f0f0f0;'>";

echo "<h1 style='color: green;'>✅ PHP is Working!</h1>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Test basic server info
echo "<h2>Server Information</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='padding: 10px; background: #ddd;'>Variable</th><th style='padding: 10px; background: #ddd;'>Value</th></tr>";

$server_vars = [
    'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'Not set',
    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'Not set',
    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'Not set',
    'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'] ?? 'Not set',
    'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'Not set',
    'HTTPS' => isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'Not set'
];

foreach ($server_vars as $key => $value) {
    echo "<tr><td style='padding: 10px;'>$key</td><td style='padding: 10px;'>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

// Test file existence without including
echo "<h2>File Existence Test</h2>";
$files = [
    'db.php',
    'config.php',
    'head.php',
    'script.php',
    'admin/index.php',
    '.htaccess'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
    echo "<p style='color: $color;'>$status - $file</p>";
}

// Test basic database connection without includes
echo "<h2>Direct Database Test</h2>";

// Try to detect environment
$host = $_SERVER['HTTP_HOST'] ?? '';
$is_localhost = (
    strpos($host, 'localhost') !== false ||
    strpos($host, '127.0.0.1') !== false ||
    strpos($_SERVER['DOCUMENT_ROOT'] ?? '', 'xampp') !== false ||
    strpos($_SERVER['DOCUMENT_ROOT'] ?? '', 'htdocs') !== false
);

echo "<p><strong>Detected Environment:</strong> " . ($is_localhost ? 'Localhost' : 'Production') . "</p>";

// Database credentials based on environment
if ($is_localhost) {
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'inspiremental';
} else {
    $db_host = 'localhost';
    $db_user = 'yaridagr_inspiremental';
    $db_pass = 'n*dMFX=i0-iq';
    $db_name = 'yaridagr_inspiremental';
}

echo "<p><strong>Database Host:</strong> $db_host</p>";
echo "<p><strong>Database User:</strong> $db_user</p>";
echo "<p><strong>Database Name:</strong> $db_name</p>";

// Test database connection
try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    
    if ($con->connect_error) {
        echo "<p style='color: red;'>❌ Database Connection Failed: " . $con->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>✅ Database Connected Successfully!</p>";
        
        // Test basic query
        $result = $con->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p style='color: green;'>✅ Users table accessible - Total users: " . $row['count'] . "</p>";
            
            // Check for admin users
            $admin_result = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'");
            if ($admin_result) {
                $admin_row = $admin_result->fetch_assoc();
                echo "<p style='color: blue;'>ℹ️ Admin users: " . $admin_row['count'] . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Database connected but query failed: " . $con->error . "</p>";
        }
        
        $con->close();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}

// Test session
echo "<h2>Session Test</h2>";
try {
    // Session already started at the top
    echo "<p style='color: green;'>✅ Session started successfully</p>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p style='color: green;'>✅ User session exists</p>";
        echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
        echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
        echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No user session found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Session Error: " . $e->getMessage() . "</p>";
}

// Test include files one by one
echo "<h2>Include File Test</h2>";

$include_files = ['db.php', 'config.php'];

foreach ($include_files as $file) {
    echo "<h3>Testing: $file</h3>";
    
    if (!file_exists($file)) {
        echo "<p style='color: red;'>❌ File does not exist</p>";
        continue;
    }
    
    try {
        // Capture any output/errors
        ob_start();
        $error_before = error_get_last();
        
        include $file;
        
        $output = ob_get_clean();
        $error_after = error_get_last();
        
        if ($error_after && $error_after !== $error_before) {
            echo "<p style='color: red;'>❌ Error including $file:</p>";
            echo "<pre style='background: #ffe6e6; padding: 10px;'>" . htmlspecialchars($error_after['message']) . "</pre>";
        } else {
            echo "<p style='color: green;'>✅ $file included successfully</p>";
            if (!empty($output)) {
                echo "<p>Output:</p>";
                echo "<pre style='background: #e6ffe6; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception including $file: " . htmlspecialchars($e->getMessage()) . "</p>";
    } catch (Error $e) {
        echo "<p style='color: red;'>❌ Fatal error including $file: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Quick links
echo "<h2>Quick Actions</h2>";
echo "<p><a href='admin/index.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Try Admin Panel</a></p>";
echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Login Page</a></p>";
echo "<p><a href='index.php' style='background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Main Site</a></p>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3 style='color: #856404;'>🔍 What This Test Shows</h3>";
echo "<p>This simple test helps identify:</p>";
echo "<ul>";
echo "<li>If PHP is working correctly</li>";
echo "<li>If database connection works</li>";
echo "<li>If required files exist</li>";
echo "<li>Which specific file is causing the 500 error</li>";
echo "<li>Environment detection issues</li>";
echo "</ul>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
