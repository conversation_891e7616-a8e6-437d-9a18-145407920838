<?php
/**
 * Emergency Error Display Enabler
 * This file forces PHP to display errors for debugging
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🚨 Error Display Enabled</h1>";
echo "<p>PHP error display has been enabled for debugging.</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Error Reporting Level:</strong> " . error_reporting() . "</p>";
echo "<p><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</p>";

echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='admin/debug.php'>Admin Debug Tool</a></li>";
echo "<li><a href='admin/test-minimal.php'>Minimal Admin Test</a></li>";
echo "<li><a href='admin/index.php'>Admin Panel</a></li>";
echo "<li><a href='index.php'>Main Site</a></li>";
echo "</ul>";

echo "<h2>Test Basic Functionality</h2>";
try {
    include 'db.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

try {
    include 'config.php';
    echo "<p style='color: green;'>✅ Config loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Instructions</h2>";
echo "<ol>";
echo "<li>Try accessing the admin panel now: <a href='admin/index.php'>Admin Panel</a></li>";
echo "<li>If you still get a 500 error, check the debug tools above</li>";
echo "<li>Look for any error messages that are now displayed</li>";
echo "<li>Once debugging is complete, delete this file for security</li>";
echo "</ol>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 20px 0; border-radius: 5px;'>";
echo "<strong>⚠️ Security Warning:</strong> This file enables error display which can expose sensitive information. ";
echo "Delete this file after debugging is complete.";
echo "</div>";
?>
