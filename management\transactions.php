<?php
// Transaction management in management directory
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
} else {
    $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) die("Database failed: " . $con->connect_error);
    $con->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: ../simple-login.php');
    exit();
}

// Check if transactions table exists
$table_check = $con->query("SHOW TABLES LIKE 'transactions'");
$transactions_exist = $table_check->num_rows > 0;

$transactions = [];
if ($transactions_exist) {
    // Fetch transactions from the database
    $result = $con->query("
        SELECT t.*, u.username 
        FROM transactions t 
        LEFT JOIN users u ON t.user_id = u.id 
        ORDER BY t.created_at DESC
    ");
    if ($result) {
        $transactions = $result->fetch_all(MYSQLI_ASSOC);
    }
}

// Handle transaction deletion
if (isset($_POST['delete_transaction'])) {
    $transaction_id = (int)$_POST['transaction_id'];
    $con->query("DELETE FROM transactions WHERE id = $transaction_id");
    $message = "Transaction deleted successfully";
    header("Location: transactions.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>بەڕێوەبردنی مامەڵەکان - ئیلهامبەخشی دەروونی</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @font-face {
            font-family: 'Rudaw';
            src: url('../assets/fonts/Rudaw.woff2') format('woff2'),
                 url('../assets/fonts/Rudaw.woff') format('woff');
            font-weight: normal;
            font-style: normal;
        }
        body {
            font-family: 'Rudaw', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .navbar-custom { background: linear-gradient(135deg, #667eea, #764ba2) !important; }
        .table th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-exchange-alt"></i> بەڕێوەبردنی مامەڵەکان
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-tachometer-alt"></i> سەرەکی
                </a>
                <a class="nav-link" href="users.php">
                    <i class="fas fa-users"></i> بەکارهێنەران
                </a>
                <a class="nav-link" href="tests.php">
                    <i class="fas fa-clipboard-list"></i> تاقیکردنەوەکان
                </a>
                <a class="nav-link" href="../index.php">
                    <i class="fas fa-home"></i> ماڵپەڕ
                </a>
                <a class="nav-link" href="../simple-login.php?logout=1">
                    <i class="fas fa-sign-out-alt"></i> چوونەدەرەوە
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <?php if (isset($message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h1><i class="fas fa-exchange-alt"></i> بەڕێوەبردنی مامەڵەکان</h1>
                        <p class="text-muted">بەڕێوەبردن و چاودێری هەموو مامەڵە داراییەکان</p>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!$transactions_exist): ?>
            <!-- No Transactions Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                <h3>خشتەی مامەڵەکان بوونی نییە</h3>
                                <p>خشتەی مامەڵەکان لە بنکەی زانیاریەکان دروست نەکراوە.</p>
                                <p>ئەگەر پێویستت بە بەڕێوەبردنی مامەڵەکان هەیە، پەیوەندی بە گەشەپێدەرەکەوە بکە.</p>
                            </div>
                            
                            <div class="mt-4">
                                <h5>بەدیلەکان:</h5>
                                <div class="d-grid gap-2 d-md-block">
                                    <a href="users.php" class="btn btn-primary">
                                        <i class="fas fa-users"></i> بەڕێوەبردنی بەکارهێنەران
                                    </a>
                                    <a href="tests.php" class="btn btn-success">
                                        <i class="fas fa-clipboard-list"></i> بەڕێوەبردنی تاقیکردنەوەکان
                                    </a>
                                    <a href="index.php" class="btn btn-secondary">
                                        <i class="fas fa-tachometer-alt"></i> گەڕانەوە بۆ سەرەکی
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3><?php echo count($transactions); ?></h3>
                            <p>کۆی گشتی مامەڵەکان</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <?php
                            $today_transactions = 0;
                            foreach ($transactions as $transaction) {
                                if (date('Y-m-d', strtotime($transaction['created_at'])) == date('Y-m-d')) {
                                    $today_transactions++;
                                }
                            }
                            ?>
                            <h3><?php echo $today_transactions; ?></h3>
                            <p>مامەڵەکانی ئەمڕۆ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <?php
                            $total_amount = 0;
                            foreach ($transactions as $transaction) {
                                $total_amount += $transaction['amount'] ?? 0;
                            }
                            ?>
                            <h3><?php echo number_format($total_amount); ?></h3>
                            <p>کۆی گشتی بڕ</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transactions Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-list"></i> لیستی مامەڵەکان</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($transactions)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ژمارە</th>
                                                <th>بەکارهێنەر</th>
                                                <th>جۆر</th>
                                                <th>بڕ</th>
                                                <th>وەسف</th>
                                                <th>بەروار</th>
                                                <th>کردارەکان</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($transactions as $transaction): ?>
                                            <tr>
                                                <td><?php echo $transaction['id']; ?></td>
                                                <td>
                                                    <?php echo htmlspecialchars($transaction['username'] ?? 'نەناسراو'); ?>
                                                    <small class="text-muted d-block">ID: <?php echo $transaction['user_id']; ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo ($transaction['type'] ?? '') === 'credit' ? 'success' : 'danger'; ?>">
                                                        <?php echo htmlspecialchars($transaction['type'] ?? 'نەناسراو'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo number_format($transaction['amount'] ?? 0); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($transaction['description'] ?? 'بێ وەسف'); ?>
                                                </td>
                                                <td>
                                                    <?php echo date('Y/m/d H:i', strtotime($transaction['created_at'])); ?>
                                                </td>
                                                <td>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="transaction_id" value="<?php echo $transaction['id']; ?>">
                                                        <button type="submit" name="delete_transaction" class="btn btn-sm btn-danger" 
                                                                onclick="return confirm('ئایا دڵنیایت لە سڕینەوەی ئەم مامەڵەیە؟')">
                                                            <i class="fas fa-trash"></i> سڕینەوە
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    هیچ مامەڵەیەک نەدۆزرایەوە
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
