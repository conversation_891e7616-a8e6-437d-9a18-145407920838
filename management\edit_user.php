<?php
// Edit user in management directory
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Direct database connection
$host = $_SERVER['HTTP_HOST'] ?? '';
if (strpos($host, 'localhost') !== false) {
    $db_host = 'localhost'; $db_user = 'root'; $db_pass = ''; $db_name = 'inspiremental';
} else {
    $db_host = 'localhost'; $db_user = 'yaridagr_inspiremental'; $db_pass = 'n*dMFX=i0-iq'; $db_name = 'yaridagr_inspiremental';
}

try {
    $con = new mysqli($db_host, $db_user, $db_pass, $db_name);
    if ($con->connect_error) die("Database failed: " . $con->connect_error);
    $con->set_charset("utf8mb4");
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Check authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: ../simple-login.php');
    exit();
}

// Get user ID from URL
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch user data from the database
$query = "SELECT id, username, email, role, credits FROM users WHERE id = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    echo "<div class='alert alert-danger'>بەکارهێنەر نەدۆزرایەوە.</div>";
    exit();
}

// Handle form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $role = $_POST['role'];
    $credits = (int)$_POST['credits'];
    
    // Validate input
    if (empty($username) || empty($email)) {
        $message = "ناو و ئیمەیڵ پێویستن.";
    } else {
        // Check if email already exists for another user
        $check_query = "SELECT id FROM users WHERE email = ? AND id != ?";
        $check_stmt = $con->prepare($check_query);
        $check_stmt->bind_param("si", $email, $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $message = "ئەم ئیمەیڵە پێشتر بەکارهاتووە.";
        } else {
            // Update user
            $update_query = "UPDATE users SET username = ?, email = ?, role = ?, credits = ? WHERE id = ?";
            $update_stmt = $con->prepare($update_query);
            $update_stmt->bind_param("sssii", $username, $email, $role, $credits, $user_id);
            
            if ($update_stmt->execute()) {
                $message = "زانیاریەکانی بەکارهێنەر بە سەرکەوتوویی نوێکرانەوە.";
                // Refresh user data
                $user['username'] = $username;
                $user['email'] = $email;
                $user['role'] = $role;
                $user['credits'] = $credits;
            } else {
                $message = "هەڵەیەک ڕوویدا لە نوێکردنەوەی زانیاریەکان.";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>دەستکاریکردنی بەکارهێنەر - ئیلهامبەخشی دەروونی</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @font-face {
            font-family: 'Rudaw';
            src: url('../assets/fonts/Rudaw.woff2') format('woff2'),
                 url('../assets/fonts/Rudaw.woff') format('woff');
            font-weight: normal;
            font-style: normal;
        }
        body {
            font-family: 'Rudaw', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { padding: 2rem 0; }
        .card { border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .navbar-custom { background: linear-gradient(135deg, #667eea, #764ba2) !important; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="users.php">
                <i class="fas fa-user-edit"></i> دەستکاریکردنی بەکارهێنەر
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-tachometer-alt"></i> سەرەکی
                </a>
                <a class="nav-link" href="users.php">
                    <i class="fas fa-users"></i> بەکارهێنەران
                </a>
                <a class="nav-link" href="../index.php">
                    <i class="fas fa-home"></i> ماڵپەڕ
                </a>
                <a class="nav-link" href="../simple-login.php?logout=1">
                    <i class="fas fa-sign-out-alt"></i> چوونەدەرەوە
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo strpos($message, 'سەرکەوتوویی') !== false ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <i class="fas fa-<?php echo strpos($message, 'سەرکەوتوویی') !== false ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Edit User Form -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-edit"></i> دەستکاریکردنی بەکارهێنەر</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">ناوی بەکارهێنەر</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ئیمەیڵ</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="role" class="form-label">ڕۆڵ</label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="User" <?php echo $user['role'] === 'User' ? 'selected' : ''; ?>>بەکارهێنەر</option>
                                            <option value="Admin" <?php echo $user['role'] === 'Admin' ? 'selected' : ''; ?>>بەڕێوەبەر</option>
                                            <option value="Doctor" <?php echo $user['role'] === 'Doctor' ? 'selected' : ''; ?>>پزیشک</option>
                                            <option value="Counsellor" <?php echo $user['role'] === 'Counsellor' ? 'selected' : ''; ?>>ڕاوێژکار</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="credits" class="form-label">کرێدیت</label>
                                        <input type="number" class="form-control" id="credits" name="credits" 
                                               value="<?php echo $user['credits'] ?? 0; ?>" min="0" max="1000">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>زانیاری:</strong> ئەگەر ڕۆڵی بەکارهێنەر بگۆڕیت، ئەو دەسەڵاتە نوێیانەی دەبێت کە تایبەتە بەو ڕۆڵە.
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="users.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> پاشگەزبوونەوە
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> پاشەکەوتکردن
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Information -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> زانیاریەکانی بەکارهێنەر</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>ژمارەی بەکارهێنەر:</strong></td>
                                        <td><?php echo $user['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>ناوی ئێستا:</strong></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>ئیمەیڵی ئێستا:</strong></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>ڕۆڵی ئێستا:</strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['role'] === 'Admin' ? 'danger' : ($user['role'] === 'Doctor' ? 'info' : ($user['role'] === 'Counsellor' ? 'warning' : 'secondary')); ?>">
                                                <?php echo $user['role']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>کرێدیتی ئێستا:</strong></td>
                                        <td><?php echo $user['credits'] ?? 0; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>دەستکاری:</strong></td>
                                        <td>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="text-warning">خۆت</span>
                                            <?php else: ?>
                                                <span class="text-success">ڕێگەپێدراو</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
